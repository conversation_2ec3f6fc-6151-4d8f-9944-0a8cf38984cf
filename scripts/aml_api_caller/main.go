package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strings"

	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/vendorgateway/aml"
)

/*********************************************************************************************
	Instructions:
		1. Set the `env` for which you want to call the API.
		2. Set the `apiType` to specify which AML API to call:
		   - "InitiateScreening" for InitiateScreening API
		   - "ListCases" for ListCases API  
		   - "GetCaseDetails" for GetCaseDetails API
		3. Set the `apiParams` as JSON string with the required parameters
		4. Run the script
***********************************************************************************************/

var (
	env       = flag.String("env", cfg.StagingEnv, "Environment to run against (e.g. 'qa', 'staging', 'demo')")
	apiType   = flag.String("apiType", "", "API type to call: InitiateScreening, ListCases, GetCaseDetails")
	apiParams = flag.String("apiParams", "", "JSON string containing API parameters")
)

type AMLClient struct {
	client aml.AmlClient
}

func main() {
	flag.Parse()

	if *apiType == "" {
		fmt.Println("Error: apiType is required")
		fmt.Println("Available API types: InitiateScreening, ListCases, GetCaseDetails")
		os.Exit(1)
	}

	if *apiParams == "" {
		fmt.Println("Error: apiParams is required")
		os.Exit(1)
	}

	// Set environment variables
	err1 := os.Setenv("ENVIRONMENT", *env)
	err2 := os.Setenv("CONFIG_DIR", "./../be-common/pkg/cfg/config")
	err3 := os.Setenv("DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP", "TRUE")
	if err1 != nil || err2 != nil || err3 != nil {
		panic(fmt.Sprintf("error in setting env variables: err1: %v | err2: %v | err3: %v", err1, err2, err3))
	}

	// Initialize logger
	logger.Init(*env)

	// Set up gRPC connection
	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	
	amlClient := &AMLClient{
		client: aml.NewAmlClient(vgConn),
	}

	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))

	fmt.Printf("Calling AML API: %s\n", *apiType)
	fmt.Printf("Environment: %s\n", *env)
	fmt.Printf("Parameters: %s\n", *apiParams)

	var response proto.Message
	var err error

	switch strings.ToLower(*apiType) {
	case "initiatescreening":
		response, err = callInitiateScreening(ctx, amlClient, *apiParams)
	case "listcases":
		response, err = callListCases(ctx, amlClient, *apiParams)
	case "getcasedetails":
		response, err = callGetCaseDetails(ctx, amlClient, *apiParams)
	default:
		fmt.Printf("Error: Unknown API type '%s'\n", *apiType)
		fmt.Println("Available API types: InitiateScreening, ListCases, GetCaseDetails")
		os.Exit(1)
	}

	if err != nil {
		fmt.Printf("Error calling API: %v\n", err)
		os.Exit(1)
	}

	// Print response
	jsonRes, err := json.MarshalIndent(response, "", "  ")
	if err == nil {
		fmt.Println("------------ JSON Response ----------------")
		fmt.Println(string(jsonRes))
	} else {
		fmt.Println("------------ Raw Response ----------------")
		fmt.Println(response)
		fmt.Printf("Error in JSON marshalling: %v\n", err)
	}
}

func callInitiateScreening(ctx context.Context, client *AMLClient, params string) (proto.Message, error) {
	// Parse parameters for InitiateScreening
	var paramMap map[string]interface{}
	if err := json.Unmarshal([]byte(params), &paramMap); err != nil {
		return nil, fmt.Errorf("error parsing parameters: %v", err)
	}

	// Build request with mandatory fields only
	req := &aml.InitiateScreeningRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_TSS_CLOUD, // Default vendor
		},
	}

	// Set mandatory fields
	if owner, ok := paramMap["owner"].(string); ok {
		switch strings.ToUpper(owner) {
		case "EPIFI":
			req.Owner = commontypes.Owner_EPIFI
		case "STOCKGUARDIAN":
			req.Owner = commontypes.Owner_STOCKGUARDIAN
		default:
			req.Owner = commontypes.Owner_EPIFI // Default
		}
	} else {
		req.Owner = commontypes.Owner_EPIFI // Default
	}

	if userId, ok := paramMap["user_id"].(string); ok {
		req.UserId = userId
	}

	if vendorRequestId, ok := paramMap["vendor_request_id"].(string); ok {
		req.VendorRequestId = vendorRequestId
	}

	if product, ok := paramMap["product"].(string); ok {
		switch strings.ToUpper(product) {
		case "SAVINGS":
			req.Product = aml.Product_PRODUCT_SAVINGS
		case "WEALTH":
			req.Product = aml.Product_PRODUCT_WEALTH
		default:
			req.Product = aml.Product_PRODUCT_SAVINGS // Default
		}
	} else {
		req.Product = aml.Product_PRODUCT_SAVINGS // Default
	}

	if purpose, ok := paramMap["purpose"].(string); ok {
		switch strings.ToUpper(purpose) {
		case "INITIAL_SCREENING":
			req.Purpose = aml.Purpose_PURPOSE_INITIAL_SCREENING
		case "CONTINUOUS_SCREENING":
			req.Purpose = aml.Purpose_PURPOSE_CONTINUOUS_SCREENING
		default:
			req.Purpose = aml.Purpose_PURPOSE_INITIAL_SCREENING // Default
		}
	} else {
		req.Purpose = aml.Purpose_PURPOSE_INITIAL_SCREENING // Default
	}

	// Set user details (mandatory)
	if userDetails, ok := paramMap["user_details"].(map[string]interface{}); ok {
		req.UserDetails = buildCustomerDetails(userDetails)
	} else {
		return nil, fmt.Errorf("user_details is mandatory for InitiateScreening")
	}

	return client.client.InitiateScreening(ctx, req)
}

func callListCases(ctx context.Context, client *AMLClient, params string) (proto.Message, error) {
	// Parse parameters for ListCases
	var paramMap map[string]interface{}
	if err := json.Unmarshal([]byte(params), &paramMap); err != nil {
		return nil, fmt.Errorf("error parsing parameters: %v", err)
	}

	req := &aml.ListCasesRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_TSS_CLOUD,
		},
	}

	// Set owner
	if owner, ok := paramMap["owner"].(string); ok {
		switch strings.ToUpper(owner) {
		case "EPIFI":
			req.Owner = commontypes.Owner_EPIFI
		case "STOCKGUARDIAN":
			req.Owner = commontypes.Owner_STOCKGUARDIAN
		default:
			req.Owner = commontypes.Owner_EPIFI
		}
	} else {
		req.Owner = commontypes.Owner_EPIFI
	}

	// Set case categories (mandatory)
	if categories, ok := paramMap["case_categories"].([]interface{}); ok {
		for _, cat := range categories {
			if catStr, ok := cat.(string); ok {
				switch strings.ToUpper(catStr) {
				case "SCREENING":
					req.CaseCategories = append(req.CaseCategories, aml.CaseCategory_CASE_CATEGORY_SCREENING)
				}
			}
		}
	} else {
		// Default case category
		req.CaseCategories = []aml.CaseCategory{aml.CaseCategory_CASE_CATEGORY_SCREENING}
	}

	// Set case types (mandatory)
	if types, ok := paramMap["case_types"].([]interface{}); ok {
		for _, typ := range types {
			if typStr, ok := typ.(string); ok {
				switch strings.ToUpper(typStr) {
				case "INDIVIDUAL":
					req.CaseTypes = append(req.CaseTypes, aml.CaseType_CASE_TYPE_INDIVIDUAL)
				}
			}
		}
	} else {
		// Default case type
		req.CaseTypes = []aml.CaseType{aml.CaseType_CASE_TYPE_INDIVIDUAL}
	}

	// Note: from_date_time and to_date_time would need to be set based on parameters
	// For simplicity, using current time range in this example

	return client.client.ListCases(ctx, req)
}

func callGetCaseDetails(ctx context.Context, client *AMLClient, params string) (proto.Message, error) {
	// Parse parameters for GetCaseDetails
	var paramMap map[string]interface{}
	if err := json.Unmarshal([]byte(params), &paramMap); err != nil {
		return nil, fmt.Errorf("error parsing parameters: %v", err)
	}

	req := &aml.GetCaseDetailsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_TSS_CLOUD,
		},
	}

	// Set owner
	if owner, ok := paramMap["owner"].(string); ok {
		switch strings.ToUpper(owner) {
		case "EPIFI":
			req.Owner = commontypes.Owner_EPIFI
		case "STOCKGUARDIAN":
			req.Owner = commontypes.Owner_STOCKGUARDIAN
		default:
			req.Owner = commontypes.Owner_EPIFI
		}
	} else {
		req.Owner = commontypes.Owner_EPIFI
	}

	// Set case ID (mandatory)
	if caseId, ok := paramMap["case_id"].(string); ok {
		req.CaseId = caseId
	} else {
		return nil, fmt.Errorf("case_id is mandatory for GetCaseDetails")
	}

	return client.client.GetCaseDetails(ctx, req)
}

func buildCustomerDetails(userDetails map[string]interface{}) *aml.CustomerDetails {
	details := &aml.CustomerDetails{}

	// Set name (mandatory)
	if name, ok := userDetails["name"].(map[string]interface{}); ok {
		details.Name = &commontypes.Name{}
		if firstName, ok := name["first_name"].(string); ok {
			details.Name.FirstName = firstName
		}
		if lastName, ok := name["last_name"].(string); ok {
			details.Name.LastName = lastName
		}
	}

	// Set nationality (mandatory)
	if nationality, ok := userDetails["nationality"].(string); ok {
		switch strings.ToUpper(nationality) {
		case "INDIAN":
			details.Nationality = commontypes.Nationality_INDIAN
		default:
			details.Nationality = commontypes.Nationality_INDIAN
		}
	} else {
		details.Nationality = commontypes.Nationality_INDIAN // Default
	}

	// Set optional fields
	if panNumber, ok := userDetails["pan_number"].(string); ok {
		details.PanNumber = panNumber
	}

	if email, ok := userDetails["email"].(string); ok {
		details.Email = email
	}

	return details
}
