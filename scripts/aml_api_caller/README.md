# AML API Caller Script

This script allows you to call various AML (Anti-Money Laundering) APIs in the vendorgateway/aml service with different API types and parameters.

## Supported API Types

1. **InitiateScreening** - Initiate screening a user's details against money-laundering, terrorist financing, and/or adverse-media activities
2. **ListCases** - List cases with information for cases that are created or modified in the given date range
3. **GetCaseDetails** - Get details of a specific case ID

Note: ScreenCustomer API is not supported as per requirements.

## Usage

```bash
go run scripts/aml_api_caller/main.go -env=<environment> -apiType=<api_type> -apiParams='<json_params>'
```

### Parameters

- `-env`: Environment to run against (default: "staging")
  - Options: "qa", "staging", "demo", "prod"
- `-apiType`: API type to call (required)
  - Options: "InitiateScreening", "ListCases", "GetCaseDetails"
- `-apiParams`: JSON string containing API parameters (required)

## Examples

### 1. InitiateScreening API

```bash
go run scripts/aml_api_caller/main.go \
  -env=staging \
  -apiType=InitiateScreening \
  -apiParams='{
    "owner": "EPIFI",
    "user_id": "test-user-123",
    "vendor_request_id": "req-123456",
    "product": "SAVINGS",
    "purpose": "INITIAL_SCREENING",
    "user_details": {
      "name": {
        "first_name": "John",
        "last_name": "Doe"
      },
      "nationality": "INDIAN",
      "pan_number": "**********",
      "email": "<EMAIL>"
    }
  }'
```

### 2. ListCases API

```bash
go run scripts/aml_api_caller/main.go \
  -env=staging \
  -apiType=ListCases \
  -apiParams='{
    "owner": "EPIFI",
    "case_categories": ["SCREENING"],
    "case_types": ["INDIVIDUAL"]
  }'
```

### 3. GetCaseDetails API

```bash
go run scripts/aml_api_caller/main.go \
  -env=staging \
  -apiType=GetCaseDetails \
  -apiParams='{
    "owner": "EPIFI",
    "case_id": "case-123456"
  }'
```

## InitiateScreening API Parameters

### Mandatory Fields
- `user_details.name` - Customer name (first_name, last_name)
- `user_details.nationality` - Customer nationality (default: "INDIAN")

### Optional Fields
- `owner` - Owner of the request ("EPIFI" or "STOCKGUARDIAN", default: "EPIFI")
- `user_id` - Unique identifier of user in caller's system
- `vendor_request_id` - Unique request ID to be passed to vendor
- `product` - Product type ("SAVINGS" or "WEALTH", default: "SAVINGS")
- `purpose` - Screening purpose ("INITIAL_SCREENING" or "CONTINUOUS_SCREENING", default: "INITIAL_SCREENING")
- `user_details.pan_number` - PAN number
- `user_details.email` - Email address

## ListCases API Parameters

### Mandatory Fields
- `case_categories` - Array of case categories (default: ["SCREENING"])
- `case_types` - Array of case types (default: ["INDIVIDUAL"])

### Optional Fields
- `owner` - Owner of the request ("EPIFI" or "STOCKGUARDIAN", default: "EPIFI")

## GetCaseDetails API Parameters

### Mandatory Fields
- `case_id` - Case ID to get details for

### Optional Fields
- `owner` - Owner of the request ("EPIFI" or "STOCKGUARDIAN", default: "EPIFI")

## Notes

1. The script uses TSS_CLOUD as the default vendor for all requests
2. All mandatory fields are validated before making the API call
3. Default values are provided for optional fields where applicable
4. The script outputs the response in JSON format for easy reading
5. Error handling is included for invalid parameters and API failures

## Environment Setup

Make sure you have the following environment variables set or the script will set them automatically:
- `ENVIRONMENT`
- `CONFIG_DIR`
- `DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP`

## Dependencies

This script requires access to:
- vendorgateway service
- Appropriate gRPC connections
- Valid configuration for the specified environment
